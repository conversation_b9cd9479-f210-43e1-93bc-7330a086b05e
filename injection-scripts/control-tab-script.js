/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

(function () {
  "use strict";

  // Simple CDP implementation for browser environment
  class CDP {
    constructor(targetInfo) {
      this.targetInfo = targetInfo;
      this.ws = null;
      this.messageId = 0;
      this.pendingMessages = new Map();
    }

    async connect() {
      if (this.ws) return;

      this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);

      return new Promise((resolve, reject) => {
        this.ws.onopen = () => {
          console.log(`Connected to target: ${this.targetInfo.title}`);
          resolve();
        };

        this.ws.onerror = reject;

        this.ws.onmessage = (event) => {
          const message = JSON.parse(event.data);

          if (message.id && this.pendingMessages.has(message.id)) {
            const { resolve, reject } = this.pendingMessages.get(message.id);
            this.pendingMessages.delete(message.id);

            if (message.error) {
              reject(new Error(message.error.message));
            } else {
              resolve(message.result);
            }
          }
        };
      });
    }

    async send(method, params = {}, sessionId = null) {
      if (!this.ws) {
        await this.connect();
      }

      const id = ++this.messageId;
      const message = { id, method, params };

      // Add sessionId if provided (for remote debugging)
      if (sessionId) {
        message.sessionId = sessionId;
      }

      return new Promise((resolve, reject) => {
        this.pendingMessages.set(id, { resolve, reject });
        this.ws.send(JSON.stringify(message));
      });
    }

    // Runtime domain
    get Runtime() {
      return {
        enable: (params = {}, sessionId = null) =>
          this.send("Runtime.enable", params, sessionId),
        evaluate: (params, sessionId = null) =>
          this.send("Runtime.evaluate", params, sessionId),
      };
    }

    // Target domain
    get Target() {
      return {
        getTargets: (params = {}, sessionId = null) =>
          this.send("Target.getTargets", params, sessionId),
        createTarget: (params, sessionId = null) =>
          this.send("Target.createTarget", params, sessionId),
        attachToTarget: (params, sessionId = null) =>
          this.send("Target.attachToTarget", params, sessionId),
        closeTarget: (params, sessionId = null) =>
          this.send("Target.closeTarget", params, sessionId),
        activateTarget: (params, sessionId = null) =>
          this.send("Target.activateTarget", params, sessionId),
      };
    }

    // Input domain
    get Input() {
      return {
        dispatchKeyEvent: (params, sessionId = null) =>
          this.send("Input.dispatchKeyEvent", params, sessionId),
        dispatchMouseEvent: (params, sessionId = null) =>
          this.send("Input.dispatchMouseEvent", params, sessionId),
      };
    }

    async close() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    }
  }

  console.log("[POC-Streaming] Control tab script initializing...");

  // Prevent multiple injections
  if (window.controlTabInjected) {
    console.log(
      "[POC-Streaming] Control tab script already injected, skipping..."
    );
    return;
  }
  window.controlTabInjected = true;

  class ControlTabManager {
    constructor() {
      this.signalingServerUrl = "ws://localhost:8080"; // Will be set dynamically
      this.websocket = null;
      this.isConnected = false;

      // WebRTC configuration
      this.rtcConfig = {
        iceServers: [
          { urls: "stun:stun.cloudflare.com:3478" },
          { urls: "stun:stun.l.google.com:19302" },
        ],
        iceCandidatePoolSize: 10,
      };

      // Connection management - refactored for multi-client support
      this.peerConnections = new Map(); // webClientId -> RTCPeerConnection (to web client)
      this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)
      this.targetTabs = new Map(); // tabId -> tabInfo
      this.activeStreams = new Map(); // tabId -> { webClientId, peerConnection }
      this.dataChannels = new Map(); // webClientId -> dataChannel
      this.webClients = new Map(); // webClientId -> { clientInfo, currentTabId }

      // CDP connection management using simple-cdp
      this.cdpClients = new Map(); // targetTabId -> CDP instance
      this.cdpSessions = new Map(); // targetTabId -> sessionId

      this.init();
    }

    async init() {
      console.log("[POC-Streaming] Initializing control tab manager...");
      await this.connectToSignalingServer();
      this.setupPageListeners();
      this.createControlTabUI();
    }

    async connectToSignalingServer() {
      try {
        console.log("[POC-Streaming] Connecting to signaling server...");
        this.websocket = new WebSocket(this.signalingServerUrl);

        this.websocket.onopen = () => {
          console.log(
            "[POC-Streaming] Control tab connected to signaling server"
          );
          this.isConnected = true;

          // Register as control tab
          this.sendMessage({
            type: "register-control-tab",
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });
        };

        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streaming] Failed to parse message:", error);
          }
        };

        this.websocket.onclose = () => {
          console.log(
            "[POC-Streaming] Control tab disconnected from signaling server"
          );
          this.isConnected = false;
          this.scheduleReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error("[POC-Streaming] Control tab WebSocket error:", error);
        };
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to connect to signaling server:",
          error
        );
        this.scheduleReconnect();
      }
    }

    scheduleReconnect() {
      console.log("[POC-Streaming] Scheduling reconnection in 5 seconds...");
      setTimeout(() => {
        this.connectToSignalingServer();
      }, 5000);
    }

    sendMessage(message) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
      } else {
        console.warn("[POC-Streaming] Cannot send message - not connected");
      }
    }

    handleMessage(message) {
      console.log(
        "[POC-Streaming] Control tab received message:",
        message.type
      );

      switch (message.type) {
        case "target-tabs-list":
          this.handleTargetTabsList(message);
          break;
        case "target-tab-registered":
          this.handleTargetTabRegistered(message);
          break;
        case "target-tab-disconnected":
          // this.handleTargetTabDisconnected(message);
          break;
        case "stream-stopped":
          // this.handleStreamStopped(message);
          break;
        case "webrtc-offer":
          this.handleWebRTCOffer(message);
          break;
        case "webrtc-answer":
          this.handleWebRTCAnswer(message);
          break;
        case "webrtc-ice-candidate":
          this.handleWebRTCIceCandidate(message);
          break;
        case "webrtc-offer-from-target":
          this.handleTargetTabOffer(message);
          break;
        case "webrtc-ice-candidate-from-target":
          this.handleTargetTabIceCandidate(message);
          break;
        case "webrtc-answer-from-web-client":
          this.handleWebClientAnswer(message);
          break;
        case "webrtc-ice-candidate-from-web-client":
          this.handleWebClientIceCandidate(message);
          break;
        case "web-client-registered":
          this.handleWebClientRegistered(message);
          break;
        case "web-client-disconnected":
          this.handleWebClientDisconnected(message);
          break;
        default:
          console.log("[POC-Streaming] Unknown message type:", message.type);
      }
    }

    handleTargetTabsList(message) {
      console.log(
        "[POC-Streaming] Received target tabs list:",
        message.targetTabs.length
      );

      // Update target tabs
      this.targetTabs.clear();
      message.targetTabs.forEach((tab) => {
        this.targetTabs.set(tab.tabId, tab);
      });
    }

    handleTargetTabRegistered(message) {
      console.log("[POC-Streaming] Target tab registered:", message.tabId);
      this.targetTabs.set(message.tabId, message);
    }

    handleTargetTabDisconnected(message) {
      console.log("[POC-Streaming] Target tab disconnected:", message.tabId);
      this.targetTabs.delete(message.tabId);

      // Clean up any active streams for this tab
      if (this.activeStreams.has(message.tabId)) {
        this.cleanupStream(message.tabId);
      }
    }

    handleWebClientRegistered(message) {
      console.log(
        "[POC-Streaming] Web client registered:",
        message.webClientId
      );

      // Store web client info
      this.webClients.set(message.webClientId, {
        clientInfo: message.metadata || {},
        currentTabId: null,
        connected: true,
      });

      // Create dedicated peer connection for this web client
      this.createPeerConnectionForWebClient(message.webClientId);
    }

    handleWebClientDisconnected(message) {
      console.log(
        "[POC-Streaming] Web client disconnected:",
        message.webClientId
      );

      // Clean up peer connection and resources for this client
      this.cleanupWebClient(message.webClientId);
    }

    handleStreamStopped(message) {
      console.log(
        "[POC-Streaming] Stream stopped for tab:",
        message.targetTabId
      );
      this.cleanupStream(message.targetTabId);
    }

    async handleWebRTCOffer() {
      console.log("[POC-Streaming] Received WebRTC offer");
      // This would be handled if control tab receives offers (not typical in this architecture)
    }

    async handleWebRTCAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer for web client:",
        message.webClientId
      );

      const peerConnection = this.peerConnections.get(message.webClientId);
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established for web client:",
            message.webClientId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description:",
            error
          );
        }
      }
    }

    async handleWebRTCIceCandidate(message) {
      const peerConnection = this.peerConnections.get(message.webClientId);
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error("[POC-Streaming] Failed to add ICE candidate:", error);
        }
      }
    }

    async handleTargetTabOffer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC offer from target tab:",
        message
      );

      // Create stream info from the message since target tab is initiating
      const streamInfo = {
        targetTabId: message.targetTabId,
        status: "connecting",
      };

      // Create peer connection to target tab
      const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Store target connection using tabId as key
      this.targetConnections.set(message.targetTabId, targetPeerConnection);

      // Handle incoming stream from target tab
      targetPeerConnection.ontrack = (event) => {
        console.log("[POC-Streaming] Target stream event:", event);
        const [stream] = event.streams;

        // Display the stream in control tab
        this.displayStreamInControlTab(message.targetTabId, stream, streamInfo);

        // Broadcast stream to ALL connected web clients
        this.broadcastStreamToAllClients(stream, message.targetTabId);
      };

      // Handle ICE candidates from target tab
      targetPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          // Send ICE candidate back to target tab
          this.sendMessage({
            type: "webrtc-ice-candidate-to-target",
            candidate: event.candidate,
            targetTabId: streamInfo.targetTabId,
          });
        }
      };

      // Accept the offer from target tab
      try {
        await targetPeerConnection.setRemoteDescription(
          new RTCSessionDescription(message.offer)
        );
        const answer = await targetPeerConnection.createAnswer();
        await targetPeerConnection.setLocalDescription(answer);

        // Send answer back to target tab
        this.sendMessage({
          type: "webrtc-answer-to-target",
          answer: answer,
          targetTabId: streamInfo.targetTabId,
        });

        console.log("[POC-Streaming] WebRTC answer sent to target tab");
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to handle target tab offer:",
          error
        );
      }
    }

    async handleTargetTabIceCandidate(message) {
      const targetPeerConnection = this.targetConnections.get(
        message.targetTabId
      );
      if (targetPeerConnection) {
        try {
          await targetPeerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add target ICE candidate:",
            error
          );
        }
      }
    }

    async handleWebClientAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer from web client:",
        message.webClientId
      );

      const peerConnection = this.peerConnections.get(message.webClientId);
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established with web client:",
            message.webClientId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description from web client:",
            error
          );
        }
      } else {
        console.warn(
          "[POC-Streaming] No peer connection found for web client:",
          message.webClientId
        );
      }
    }

    async handleWebClientIceCandidate(message) {
      console.log(
        "[POC-Streaming] Received ICE candidate from web client:",
        message.webClientId
      );

      const peerConnection = this.peerConnections.get(message.webClientId);
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add ICE candidate from web client:",
            error
          );
        }
      } else {
        console.warn(
          "[POC-Streaming] No peer connection found for web client:",
          message.webClientId
        );
      }
    }

    createPeerConnectionForWebClient(webClientId) {
      console.log(
        "[POC-Streaming] Creating peer connection for web client:",
        webClientId
      );

      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Create data channel for user events
      const dataChannel = peerConnection.createDataChannel("userEvents", {
        ordered: true,
      });
      this.setupDataChannelHandlers(dataChannel, webClientId);
      this.dataChannels.set(webClientId, dataChannel);

      // Handle ICE candidates from web client
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate-to-web-client",
            candidate: event.candidate,
            webClientId: webClientId,
          });
        }
      };

      // Store the peer connection
      this.peerConnections.set(webClientId, peerConnection);

      console.log(
        "[POC-Streaming] Peer connection created for web client:",
        webClientId
      );
    }

    cleanupWebClient(webClientId) {
      console.log("[POC-Streaming] Cleaning up web client:", webClientId);

      // Close and remove peer connection
      const peerConnection = this.peerConnections.get(webClientId);
      if (peerConnection) {
        peerConnection.close();
        this.peerConnections.delete(webClientId);
      }

      // Clean up data channel
      const dataChannel = this.dataChannels.get(webClientId);
      if (dataChannel) {
        dataChannel.close();
        this.dataChannels.delete(webClientId);
      }

      // Remove from web clients map
      this.webClients.delete(webClientId);

      console.log("[POC-Streaming] Web client cleanup completed:", webClientId);
    }

    broadcastStreamToAllClients(stream, targetTabId) {
      console.log(
        "[POC-Streaming] Broadcasting stream to all connected web clients for tab:",
        targetTabId
      );

      // Iterate through all connected web clients
      for (const [webClientId, peerConnection] of this.peerConnections) {
        console.log(
          "[POC-Streaming] Adding/replacing tracks for web client:",
          webClientId
        );

        // Update client's current tab
        const clientInfo = this.webClients.get(webClientId);
        if (clientInfo) {
          clientInfo.currentTabId = targetTabId;
        }

        // Add or replace tracks for this client
        stream.getTracks().forEach((track) => {
          const sender = peerConnection
            .getSenders()
            .find((s) => s.track && s.track.kind === track.kind);

          if (sender) {
            console.log(
              `[POC-Streaming] Replacing ${track.kind} track for client:`,
              webClientId
            );
            sender.replaceTrack(track);
          } else {
            console.log(
              `[POC-Streaming] Adding ${track.kind} track for client:`,
              webClientId
            );
            peerConnection.addTrack(track, stream);
          }
        });

        // Create offer to this web client
        this.createOfferToWebClient(targetTabId, webClientId);
      }

      console.log(
        "[POC-Streaming] Stream broadcast completed to",
        this.peerConnections.size,
        "clients"
      );
    }

    async createOfferToWebClient(tabId, webClientId) {
      const peerConnection = this.peerConnections.get(webClientId);
      if (!peerConnection) {
        console.error(
          "[POC-Streaming] No peer connection found for web client:",
          webClientId
        );
        return;
      }

      try {
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Send offer to web client
        this.sendMessage({
          type: "webrtc-offer-to-web-client",
          offer: offer,
          targetClientId: webClientId,
          tabId: tabId,
          fromClientId: this.clientId,
        });

        console.log(
          "[POC-Streaming] WebRTC offer sent to web client:",
          webClientId
        );
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to create offer to web client:",
          error
        );
      }
    }

    cleanupStream(tabId) {
      console.log("[POC-Streaming] Cleaning up stream for tab:", tabId);

      // Clean up target connection
      const targetConnection = this.targetConnections.get(tabId);
      if (targetConnection) {
        targetConnection.close();
        this.targetConnections.delete(tabId);
      }

      // Update all web clients to remove tracks from this tab
      for (const [webClientId, clientInfo] of this.webClients) {
        if (clientInfo.currentTabId === tabId) {
          clientInfo.currentTabId = null;

          // Remove tracks from this client's peer connection
          const peerConnection = this.peerConnections.get(webClientId);
          if (peerConnection) {
            peerConnection.getSenders().forEach((sender) => {
              if (sender.track) {
                peerConnection.removeTrack(sender);
              }
            });
          }
        }
      }

      // Remove from UI
      const streamElement = document.getElementById(`poc-stream-${tabId}`);
      if (streamElement) {
        streamElement.remove();
      }

      // Add "no streams" message if no streams left
      const streamsContainer = document.getElementById("poc-streams-container");
      if (streamsContainer && streamsContainer.children.length === 0) {
        streamsContainer.innerHTML =
          '<div style="color: #666; font-style: italic;">No active streams</div>';
      }

      this.activeStreams.delete(tabId);
    }

    createControlTabUI() {
      // Create a floating control panel for the control tab
      const controlPanel = document.createElement("div");
      controlPanel.id = "poc-control-panel";
      controlPanel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 600px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 8px;
        padding: 16px;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        overflow-y: auto;
      `;

      controlPanel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>
          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>
        </div>
        <div id="poc-panel-content">
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>
            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>
            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No active streams</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>
            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No target tabs</div>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(controlPanel);

      // Add toggle functionality
      const toggleBtn = document.getElementById("poc-toggle-panel");
      const panelContent = document.getElementById("poc-panel-content");
      let isCollapsed = false;

      toggleBtn.addEventListener("click", () => {
        isCollapsed = !isCollapsed;
        panelContent.style.display = isCollapsed ? "none" : "block";
        toggleBtn.textContent = isCollapsed ? "+" : "−";
        controlPanel.style.height = isCollapsed ? "auto" : "";
      });

      // Update connection status
      this.updateConnectionStatus("Connected");
    }

    updateConnectionStatus(status) {
      const statusElement = document.getElementById("poc-connection-status");
      if (statusElement) {
        statusElement.textContent = status;
        statusElement.style.color =
          status === "Connected" ? "#4CAF50" : "#ff9800";
      }
    }

    displayStreamInControlTab(tabId, mediaStream, streamInfo) {
      console.log(
        "[POC-Streaming] Displaying stream in control tab for tab:",
        tabId
      );

      const streamsContainer = document.getElementById("poc-streams-container");
      if (!streamsContainer) {
        console.warn("[POC-Streaming] Streams container not found");
        return;
      }

      // Remove "no streams" message if present
      const noStreamsMsg = streamsContainer.querySelector(
        '[style*="font-style: italic"]'
      );
      if (
        noStreamsMsg &&
        noStreamsMsg.textContent.includes("No active streams")
      ) {
        noStreamsMsg.remove();
      }

      // Create stream display element
      const streamElement = document.createElement("div");
      streamElement.id = `poc-stream-${tabId}`;
      streamElement.style.cssText = `
        margin-bottom: 12px;
        padding: 8px;
        border: 1px solid #333;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      `;

      streamElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">
          📺 Tab ${tabId.substring(0, 8)}...
        </div>
        <video
          id="poc-video-${tabId}"
          autoplay
          muted
          playsinline
          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"
        ></video>
        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">
          Target: ${streamInfo.targetTabId || "Unknown"}
        </div>
      `;

      streamsContainer.appendChild(streamElement);

      // Set the video source
      const video = document.getElementById(`poc-video-${tabId}`);
      if (video && mediaStream) {
        video.srcObject = mediaStream;

        video.onloadedmetadata = () => {
          console.log("[POC-Streaming] Video metadata loaded in control tab");
        };

        video.onplay = () => {
          console.log("[POC-Streaming] Video started playing in control tab");
        };

        video.onerror = (error) => {
          console.error("[POC-Streaming] Video error in control tab:", error);
        };
      }
    }

    setupPageListeners() {
      // Listen for page unload
      window.addEventListener("beforeunload", () => {
        console.log("[POC-Streaming] Control tab unloading...");

        // Clean up all streams
        for (const tabId of this.activeStreams.keys()) {
          this.cleanupStream(tabId);
        }

        // Close WebSocket
        if (this.websocket) {
          this.websocket.close();
        }
      });
    }

    /**
     * Setup data channel handlers for user event replay
     */
    setupDataChannelHandlers(dataChannel, webClientId) {
      console.log(
        "[POC-Streaming] Setting up data channel for web client:",
        webClientId
      );

      dataChannel.onopen = () => {
        console.log(
          "[POC-Streaming] Data channel opened for web client:",
          webClientId
        );
      };

      dataChannel.onclose = () => {
        console.log(
          "[POC-Streaming] Data channel closed for web client:",
          webClientId
        );
      };

      dataChannel.onerror = (error) => {
        console.error("[POC-Streaming] Data channel error:", error);
      };

      dataChannel.onmessage = (event) => {
        try {
          const userEvent = JSON.parse(event.data);
          console.log("[POC-Streaming] Received user event:", userEvent);

          // Get the current tab this client is viewing
          const clientInfo = this.webClients.get(webClientId);
          const targetTabId = clientInfo?.currentTabId;

          if (targetTabId) {
            this.handleUserEvent(userEvent, targetTabId);
          } else {
            console.warn(
              "[POC-Streaming] No target tab for user event from client:",
              webClientId
            );
          }
        } catch (error) {
          console.error("[POC-Streaming] Failed to parse user event:", error);
        }
      };
    }

    /**
     * Handle user events from web client
     */
    async handleUserEvent(userEvent, targetTabId) {
      if (userEvent.type !== "user-event") {
        console.warn("[POC-Streaming] Unknown event type:", userEvent.type);
        return;
      }

      console.log(
        "[POC-Streaming] Replaying user event on target tab:",
        targetTabId
      );

      try {
        await this.replayEventOnTargetTab(userEvent, targetTabId);
      } catch (error) {
        console.error("[POC-Streaming] Failed to replay event:", error);
      }
    }

    /**
     * Establish persistent CDP session to target tab using simple-cdp
     */
    async establishCDPSession(targetTabId) {
      if (this.cdpClients.has(targetTabId)) {
        return this.cdpClients.get(targetTabId);
      }

      try {
        console.log(
          "[POC-Streaming] Establishing CDP session to tab:",
          targetTabId
        );

        // Get target info from CDP
        const tabsResponse = await fetch("http://localhost:9222/json");
        const tabs = await tabsResponse.json();
        const targetTab = tabs.find((tab) => tab.id === targetTabId);

        if (!targetTab) {
          throw new Error(`Target tab ${targetTabId} not found`);
        }

        // Create CDP client instance
        const cdpClient = new CDP(targetTab);
        await cdpClient.connect();

        // Attach to target to create a session
        const attachResult = await cdpClient.Target.attachToTarget({
          targetId: targetTabId,
          flatten: true,
        });

        const sessionId = attachResult.sessionId;
        console.log("[POC-Streaming] CDP session established:", sessionId);

        // Store the client and session
        this.cdpClients.set(targetTabId, cdpClient);
        this.cdpSessions.set(targetTabId, sessionId);

        return cdpClient;
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to establish CDP session:",
          error
        );
        throw error;
      }
    }

    /**
     * Execute CDP Input command using simple-cdp
     */
    async executeCDPInputCommand(targetTabId, method, params) {
      try {
        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        console.log(
          `[POC-Streaming] Executing ${method} on target tab:`,
          targetTabId
        );

        // Use simple-cdp to execute the Input command
        if (method === "Input.dispatchMouseEvent") {
          return await cdpClient.Input.dispatchMouseEvent(params, sessionId);
        } else if (method === "Input.dispatchKeyEvent") {
          return await cdpClient.Input.dispatchKeyEvent(params, sessionId);
        } else {
          throw new Error(`Unsupported Input method: ${method}`);
        }
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to execute CDP Input command:",
          error
        );
        throw error;
      }
    }

    /**
     * Replay user event on target tab using CDP
     */
    async replayEventOnTargetTab(userEvent, targetTabId) {
      if (userEvent.eventType === "click") {
        await this.replayClickEvent(userEvent, targetTabId);
      } else {
        console.warn(
          "[POC-Streaming] Unsupported event type:",
          userEvent.eventType
        );
      }
    }

    /**
     * Replay click event on target tab
     */
    async replayClickEvent(userEvent, targetTabId) {
      try {
        // Get target tab dimensions
        const tabInfo = await this.getTargetTabInfo(targetTabId);
        if (!tabInfo) {
          console.error("[POC-Streaming] Could not get target tab info");
          return;
        }

        // Calculate actual coordinates in target tab
        const targetX = userEvent.x * tabInfo.width;
        const targetY = userEvent.y * tabInfo.height;

        console.log(
          `[POC-Streaming] Replaying click at (${targetX}, ${targetY}) on tab ${targetTabId}`
        );

        // Use CDP Input API for more reliable event dispatch
        console.log("[POC-Streaming] Using CDP Input API for click event");

        // Dispatch mouse down
        await this.executeCDPInputCommand(
          targetTabId,
          "Input.dispatchMouseEvent",
          {
            type: "mousePressed",
            x: Math.round(targetX),
            y: Math.round(targetY),
            button: "left",
            clickCount: 1,
            buttons: 1,
          }
        );

        // Small delay between mouse down and up
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Dispatch mouse up
        await this.executeCDPInputCommand(
          targetTabId,
          "Input.dispatchMouseEvent",
          {
            type: "mouseReleased",
            x: Math.round(targetX),
            y: Math.round(targetY),
            button: "left",
            clickCount: 1,
            buttons: 0,
          }
        );

        console.log(
          "[POC-Streaming] Click event dispatched successfully via CDP Input API"
        );
      } catch (error) {
        console.error("[POC-Streaming] Failed to replay click:", error);
      }
    }

    /**
     * Get target tab information using simple-cdp
     */
    async getTargetTabInfo(targetTabId) {
      try {
        console.log("[POC-Streaming] Getting tab info for:", targetTabId);

        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        // Use simple-cdp to evaluate script and get tab dimensions
        const result = await cdpClient.Runtime.evaluate(
          {
            expression: `({
              width: window.innerWidth,
              height: window.innerHeight,
              url: window.location.href
            })`,
            returnByValue: true,
            awaitPromise: true,
          },
          sessionId
        );
        return result.result.value;
      } catch (error) {
        console.error("[POC-Streaming] Failed to get tab info:", error);
        return { width: 1920, height: 1080, url: "unknown" };
      }
    }

    /**
     * Execute script on target tab using simple-cdp
     */
    async executeCDPScript(targetTabId, script) {
      try {
        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        console.log(
          "[POC-Streaming] Executing script on target tab:",
          targetTabId
        );

        // Use simple-cdp to evaluate script
        const result = await cdpClient.Runtime.evaluate(
          {
            expression: script,
            returnByValue: true,
          },
          sessionId
        );

        return result.result.value;
      } catch (error) {
        console.error("[POC-Streaming] Failed to execute CDP script:", error);
        return null;
      }
    }

    /**
     * Clean up CDP session for a target tab
     */
    async cleanupCDPSession(targetTabId) {
      try {
        const cdpClient = this.cdpClients.get(targetTabId);
        if (cdpClient) {
          console.log(
            "[POC-Streaming] Cleaning up CDP session for tab:",
            targetTabId
          );
          await cdpClient.close();
          this.cdpClients.delete(targetTabId);
          this.cdpSessions.delete(targetTabId);
        }
      } catch (error) {
        console.error("[POC-Streaming] Failed to cleanup CDP session:", error);
      }
    }
  }

  // Initialize the control tab manager
  window.pocControlTabManager = new ControlTabManager();

  console.log("[POC-Streaming] Control tab script initialized successfully");
})();
